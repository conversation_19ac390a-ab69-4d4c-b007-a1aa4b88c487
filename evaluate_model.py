#!/usr/bin/env python3
"""
模型评估和报告生成脚本

用于评估训练好的动态任务图模型，生成详细的评估报告
支持静态基线和动态方法的对比评估
"""

import os
import sys
import torch
import warnings
from pathlib import Path
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量优化GPU性能
os.environ['CUDA_VISIBLE_DEVICES'] = '0'
os.environ['TORCH_CUDNN_BENCHMARK'] = '1'
os.environ['OMP_NUM_THREADS'] = '8'

import pytorch_lightning as pl
import hydra
from omegaconf import DictConfig

# 优化GPU性能
torch.set_float32_matmul_precision('high')

# 导入项目模块
from src.data.datamodule import BreakfastDataModule
from src.core.lightning_module import TrainerModule
from src.core.task_graph import TaskGraph
from src.core.diff_update import DiffMLP
from src.utils.stats_computer import load_stats, verify_stats
from src.utils.metrics import compute_metrics, greedy_merge

# cereals任务的完整48个动作列表（根据Plan_Min.md规范）
CEREALS_ACTIONS = [
    "take_bowl", "take_plate", "take_cup", "take_glass", "take_cutlery",
    "take_bottle", "pour_bottle", "take_milk", "pour_milk", "take_sugar",
    "pour_sugar", "take_salt", "pour_salt", "take_oil", "pour_oil",
    "take_vinegar", "pour_vinegar", "take_cereals", "pour_cereals",
    "take_jam", "spread_jam", "take_butter", "spread_butter", "take_cheese",
    "spread_cheese", "take_bread", "cut_bread", "take_bun", "cut_bun",
    "take_orange", "squeeze_orange", "take_juice", "pour_juice",
    "take_coffee", "pour_coffee", "take_squeezer", "stir_drink",
    "put_on_fire", "take_from_fire", "put_in_oven", "take_from_oven",
    "put_in_fridge", "take_from_fridge", "put_in_microwave",
    "take_from_microwave", "clean_table", "SIL"
]

# 从1-based字符串到0-based整数的映射（根据Plan_Min.md规范）
ACTION_MAP = {str(i + 1): i for i, action in enumerate(CEREALS_ACTIONS)}


def load_model(checkpoint_path: str, cfg: DictConfig) -> TrainerModule:
    """加载训练好的模型"""
    # 加载统计数据
    prototypes, W0 = load_stats(cfg.stats_dir)
    verify_stats(prototypes, W0, cfg.model.D, cfg.model.M)
    
    # 创建模型组件
    task_graph = TaskGraph(W0, prototypes)
    diff_mlp = DiffMLP(cfg.model.D, cfg.model.M, cfg.model.H, cfg.model.alpha)
    
    # 从检查点加载模型
    model = TrainerModule.load_from_checkpoint(
        checkpoint_path,
        diff_mlp=diff_mlp,
        task_graph=task_graph,
        learning_rate=cfg.model.lr,
        diff_enabled=cfg.model.diff_enabled
    )
    
    return model


def evaluate_model(model: TrainerModule, datamodule: BreakfastDataModule, device: str = 'cuda') -> Dict:
    """评估模型性能"""
    model.eval()
    model.to(device)
    
    # 设置数据模块
    datamodule.setup('test')
    test_dataloader = datamodule.test_dataloader()
    
    all_predictions = []
    all_ground_truths = []
    
    print("开始模型评估...")
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_dataloader):
            V_padded, y_padded, lengths = batch
            if V_padded is None:
                continue
                
            V_padded = V_padded.to(device)
            y_padded = y_padded.to(device)
            lengths = lengths.to(device)
            
            # 使用模型的验证步骤进行预测
            output = model.validation_step(batch, batch_idx)
            if output:
                all_predictions.extend(output["preds"])
                all_ground_truths.extend(output["gts"])
            
            if (batch_idx + 1) % 10 == 0:
                print(f"已处理 {batch_idx + 1} 个批次")
    
    if not all_predictions:
        raise ValueError("没有获得任何预测结果，请检查数据集和模型配置")
    
    # 计算评估指标
    metrics = compute_metrics(all_predictions, all_ground_truths)
    
    print(f"评估完成，共处理 {len(all_predictions)} 个序列")
    return metrics


def generate_report(metrics: Dict, model_type: str, output_dir: str):
    """生成评估报告"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成文本报告
    report_path = os.path.join(output_dir, f"{model_type}_evaluation_report.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(f"# {model_type.upper()} 模型评估报告\n\n")
        f.write("## 评估指标\n\n")
        f.write(f"- **时序段级IoU**: {metrics['segment_iou_mean']:.4f}\n")
        f.write(f"- **帧级准确度**: {metrics['frame_accuracy_mean']:.4f}\n")
        f.write(f"- **动作覆盖率**: {metrics['action_coverage_mean']:.4f}\n")
        f.write(f"- **归一化编辑距离**: {metrics['lev_mean']:.4f}\n\n")
        
        f.write("## 指标说明\n\n")
        f.write("- **时序段级IoU**: 真正的时序重叠度，衡量预测段与真实段的时间对齐程度\n")
        f.write("- **帧级准确度**: 逐帧预测正确率，衡量细粒度预测精度\n")
        f.write("- **动作覆盖率**: 动作类型覆盖度，衡量预测动作集合与真实动作集合的重叠\n")
        f.write("- **归一化编辑距离**: 序列相似度，值越小表示序列越相似\n\n")
        
        f.write(f"## 详细统计\n\n")
        f.write(f"- 评估序列数量: {len(metrics['segment_iou_scores'])}\n")
        f.write(f"- 时序IoU标准差: {np.std(metrics['segment_iou_scores']):.4f}\n")
        f.write(f"- 帧准确度标准差: {np.std(metrics['frame_accuracy_scores']):.4f}\n")
    
    # 生成可视化图表
    plt.figure(figsize=(12, 8))
    
    # 指标分布图
    plt.subplot(2, 2, 1)
    plt.hist(metrics['segment_iou_scores'], bins=20, alpha=0.7, color='blue')
    plt.title('时序段级IoU分布')
    plt.xlabel('IoU值')
    plt.ylabel('频次')
    
    plt.subplot(2, 2, 2)
    plt.hist(metrics['frame_accuracy_scores'], bins=20, alpha=0.7, color='green')
    plt.title('帧级准确度分布')
    plt.xlabel('准确度')
    plt.ylabel('频次')
    
    plt.subplot(2, 2, 3)
    plt.hist(metrics['action_coverage_scores'], bins=20, alpha=0.7, color='orange')
    plt.title('动作覆盖率分布')
    plt.xlabel('覆盖率')
    plt.ylabel('频次')
    
    plt.subplot(2, 2, 4)
    plt.hist(metrics['lev_scores'], bins=20, alpha=0.7, color='red')
    plt.title('归一化编辑距离分布')
    plt.xlabel('编辑距离')
    plt.ylabel('频次')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f"{model_type}_metrics_distribution.png"), dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"评估报告已保存到: {report_path}")
    print(f"可视化图表已保存到: {os.path.join(output_dir, f'{model_type}_metrics_distribution.png')}")


@hydra.main(config_path="configs", config_name="config", version_base=None)
def main(cfg: DictConfig):
    """主评估函数"""
    print("=" * 80)
    print("🔍 动态任务图模型评估")
    print("=" * 80)
    
    # 设置随机种子
    pl.seed_everything(cfg.seed, workers=True)
    
    # 准备数据模块
    print("\n📊 设置数据模块...")
    dm = BreakfastDataModule(
        data_root=cfg.data_root,
        batch_size=cfg.trainer.batch_size,
        action_map=ACTION_MAP,
        num_workers=cfg.trainer.num_workers
    )
    
    # 检查模型检查点
    model_type = "dynamic" if cfg.model.diff_enabled else "static"
    checkpoint_dir = os.path.join(cfg.output_dir, "checkpoints")
    
    if not os.path.exists(checkpoint_dir):
        raise FileNotFoundError(f"检查点目录不存在: {checkpoint_dir}")
    
    # 查找最佳模型检查点
    checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.ckpt') and 'best' in f.lower()]
    if not checkpoint_files:
        checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.ckpt')]
    
    if not checkpoint_files:
        raise FileNotFoundError(f"在 {checkpoint_dir} 中未找到模型检查点文件")
    
    checkpoint_path = os.path.join(checkpoint_dir, checkpoint_files[0])
    print(f"📁 使用模型检查点: {checkpoint_path}")
    
    # 加载模型
    print("\n🧠 加载模型...")
    model = load_model(checkpoint_path, cfg)
    
    # 评估模型
    print("\n📈 开始评估...")
    metrics = evaluate_model(model, dm)
    
    # 生成报告
    print("\n📋 生成评估报告...")
    generate_report(metrics, model_type, cfg.output_dir)
    
    print("\n" + "=" * 80)
    print("✅ 模型评估完成!")
    print(f"📁 结果保存到: {cfg.output_dir}")
    print("=" * 80)


if __name__ == "__main__":
    main()
