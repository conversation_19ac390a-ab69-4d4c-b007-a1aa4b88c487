### 6. 视角融合、帧同步与标签映射实现细节

> **本节在不修改既有框架任何行的前提下，给出所有 `pass` 位置所需的核心算法/代码片段，确保疑惑一一落地。**

#### 6.1 视角识别与拼接顺序

```python
# utils/data_loader.py
import re

_CAM_RE = re.compile(r"_cam(\d+)")

def _extract_cam_id(path: str) -> int:
    """从文件名中提取相机编号（默认 cam1 < cam2 < …）。"""
    m = _CAM_RE.search(path)
    return int(m.group(1)) if m else -1  # -1 让无法识别的排在最前面
```
- **排序原则**：按提取到的相机编号升序排序。如此即可令 `MultiViewFusion.fuse_views()` **先拼 cam1 → cam2 → …**，随后在行向量维度上做 `np.mean(axis=0)`。

#### 6.2 帧同步算法

```python
# utils/data_loader.py → MultiViewFusion.sync_frames
@staticmethod
def sync_frames(view_feats: List[np.ndarray]) -> List[np.ndarray]:
    """取各视角帧索引的交集并按升序返回同步后的特征序列。"""
    # 假设第 0 列是显式帧 index，后 64 列为特征
    common_idx = set(view_feats[0][:, 0].astype(int))
    for f in view_feats[1:]:
        common_idx &= set(f[:, 0].astype(int))
    common_idx = sorted(common_idx)
    fused = []
    for f in view_feats:
        idx_to_row = {int(row[0]): row[1:] for row in f}
        fused.append(np.stack([idx_to_row[i] for i in common_idx], axis=0))
    return fused  # 每个元素形状: |common_idx| × 64
```
随后 `fuse_views()` 将 `sync_frames()` 的输出沿 **视角维**(len C) 拼接，再 `np.mean(axis=0)` 保持 **64 维**。

#### 6.3 `label_map.json` 标准格式

```json
{
  "0": "SIL",
  "1": "take_bowl",
  "2": "pour_cereals",
  "3": "pour_milk",
  "4": "stir_cereals",
  "5": "put_down_spoon"
}
```
- **键**：字符串化的整数索引（保持与 numpy `int64` 互转安全）。
- **值**：动作中文或英文名称均可，但 **文件内必须唯一**。
- **自动统计 M**：`BreakfastDataLoader.__init__` 在加载后执行 `self.num_classes = len(self.label_map)` 并向下游暴露。

---

### 7. 模型层面对疑惑的解答与代码钩子

| 疑惑 | 解决方案 | 代码落点 |
|------|----------|----------|
| **MLP_diff 输入维度** | `Diff_k = |V_k - V_prototype|` 与 `V_k` 同为 **64 维**。 | `models/mlp_diff.MLPDiff.__init__` 已固定 `input_dim = config['model']['feature_dim']` (=64)。 |
| **静态模型 bias 维度** | 运行时 `BreakfastDataLoader.num_classes -> M`，在 `StaticModel`、`DynamicModel` 构造函数里直接使用。 | 见下 `StaticModel.__init__` 样例。 |
| **类别不均衡加权** | 交叉熵 `weight` 参数可选：`torch.Tensor(class_freq).reciprocal()`；默认 `None`；在 `config.yaml` 新增 `training.class_balancing: false/true` 开关。 | `Train_Static_Model.py` & `Train_Dynamic_Model.py` → 创建 `criterion` 时读取该开关。 |

```python
# models/base extensions
class StaticModel(nn.Module):
    def __init__(self, edge_weights: torch.Tensor):
        super().__init__()
        num_classes = edge_weights.size(0)
        self.register_buffer('edge_weights', edge_weights)  # 冻结
        self.bias = nn.Parameter(torch.zeros(num_classes))

    def forward(self, current_actions: torch.Tensor):
        # 根据当前动作索引选取对应行
        logits = self.edge_weights[current_actions] + self.bias
        return logits
```

---

### 8. 评估指标实现 (包含 Hungarian 匹配与 SIL 过滤)

```python
# utils/evaluation_metrics.py (关键实现片段)
from scipy.optimize import linear_sum_assignment

SIL_ID = 0  # 假设 0 对应 SIL

class EvaluationMetrics:
    ...
    def _filter_sil(self, segs):
        return [s for s in segs if s[2] != SIL_ID]

    def compute_temporal_iou(self, pred_segs, gt_segs, threshold):
        pred_segs = self._filter_sil(pred_segs)
        gt_segs = self._filter_sil(gt_segs)
        if not pred_segs or not gt_segs:
            return 0.0
        # 构造 IoU 矩阵
        IoU_mat = np.zeros((len(gt_segs), len(pred_segs)))
        for i, g in enumerate(gt_segs):
            gs, ge, _ = g
            for j, p in enumerate(pred_segs):
                ps, pe, _ = p
                inter = max(0, min(ge, pe) - max(gs, ps) + 1)
                union = (ge - gs + 1) + (pe - ps + 1) - inter
                IoU_mat[i, j] = inter / union
        # 负值转成本
        cost = 1 - IoU_mat
        row_ind, col_ind = linear_sum_assignment(cost)
        TP, FP, FN = 0, 0, 0
        for r, c in zip(row_ind, col_ind):
            if IoU_mat[r, c] >= threshold:
                TP += IoU_mat[r, c]  # 累加交集
            else:
                FP += IoU_mat[r, c]
        FN = len(gt_segs) - TP
        union_sum = TP + FP + FN
        return TP / union_sum if union_sum else 0.0
```
- **micro IoU**：跨视频累积 `TP`, `FP`, `FN` 后再统一计算。

#### Bootstrap CI

```python
from functools import partial

    def bootstrap_confidence_interval(self, data, metric_func, n_bootstrap=500, confidence=0.95):
        stats = []
        rng = np.random.default_rng(42)
        N = len(data)
        for _ in range(n_bootstrap):
            sample_idx = rng.choice(N, N, replace=True)
            stats.append(metric_func([data[i] for i in sample_idx]))
        lower = np.percentile(stats, (1 - confidence) / 2 * 100)
        upper = np.percentile(stats, (1 + confidence) / 2 * 100)
        return float(np.mean(stats)), float(lower), float(upper)
```

---

### 9. DynamicModel 训练脚本剩余关键函数

```python
# Train/Train_Dynamic_Model.py (简要示例)
class DynamicModel(nn.Module):
    def forward(self, features, current_actions):
        diff = torch.abs(features - self.prototypes[current_actions])
        delta = self.mlp_diff(diff)
        logits = self.edge_weights[current_actions] + delta
        return logits
```

```python
class DynamicModelTrainer:
    def prepare_training_data(self):
        samples = self.data_loader.get_all_training_data()
        for s in samples:
            cur = s['current_action']
            diff = np.abs(s['features'] - self.prototypes[cur].cpu().numpy())
            s['diff'] = diff
        return samples
```
- **训练/验证 Epoch**：复用 `train_epoch` 写法与静态模型相同，只是输入多了 `features`。

---

### 10. `run_experiment.py` 总控脚本骨架

```python
"""一键跑全流程"""
import subprocess, yaml

STEPS = [
    'txt_to_npy.py',
    'Train/Train_Action_Prototype.py',
    'Train/Train_Edge_Weight.py',
    'Train/Train_Static_Model.py',
    'Train/Train_Dynamic_Model.py',
    'Test/Test_Static.py',
    'Test/Test_Dynamic.py',
    'Test/Test_Static_vs_Dynamic.py'
]

if __name__ == '__main__':
    for step in STEPS:
        print(f'>>> Running {step}')
        subprocess.run(['python', step], check=True)
```

---

### 11. 更新后的 `config.yaml` 片段

```yaml
training:
  ...
  class_balancing: false   # 若为 true 将自动按 1/freq 加权
```

---

> **至此，所有用户提出的疑惑均已在代码层面给出可复用实现示例，原有文件段落未作任何改动。后续填充业务逻辑时可直接复制粘贴上述片段替换各 `pass`。

