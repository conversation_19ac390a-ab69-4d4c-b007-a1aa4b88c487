#!/usr/bin/env python3
"""
动态差分更新方法训练脚本
专门用于训练动态任务图谱差分更新模型，针对A6000 GPU优化
"""

import os
import sys
import torch
import warnings
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量优化GPU性能
os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # 使用第一块GPU
os.environ['TORCH_CUDNN_BENCHMARK'] = '1'  # 启用cuDNN benchmark
os.environ['OMP_NUM_THREADS'] = '8'  # 设置OpenMP线程数

import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
from pytorch_lightning.loggers import TensorBoardLogger
import hydra
from omegaconf import DictConfig, OmegaConf

# 优化GPU性能 - 设置Tensor Cores精度
torch.set_float32_matmul_precision('high')

# 导入项目模块
from src.data.datamodule import BreakfastDataModule
from src.core.lightning_module import TrainerModule
from src.core.task_graph import TaskGraph
from src.core.diff_update import DiffMLP
from src.utils.stats_computer import compute_and_save_stats, load_stats, verify_stats

# cereals任务的完整48个动作列表（根据Plan_Min.md规范）
CEREALS_ACTIONS = [
    "take_bowl", "take_plate", "take_cup", "take_glass", "take_cutlery",
    "take_bottle", "pour_bottle", "take_milk", "pour_milk", "take_sugar",
    "pour_sugar", "take_salt", "pour_salt", "take_oil", "pour_oil",
    "take_vinegar", "pour_vinegar", "take_cereals", "pour_cereals",
    "take_jam", "spread_jam", "take_butter", "spread_butter", "take_cheese",
    "spread_cheese", "take_bread", "cut_bread", "take_bun", "cut_bun",
    "take_orange", "squeeze_orange", "take_juice", "pour_juice",
    "take_coffee", "pour_coffee", "take_squeezer", "stir_drink",
    "put_on_fire", "take_from_fire", "put_in_oven", "take_from_oven",
    "put_in_fridge", "take_from_fridge", "put_in_microwave",
    "take_from_microwave", "clean_table", "SIL"
]

# 从1-based字符串到0-based整数的映射（根据Plan_Min.md规范）
ACTION_MAP = {str(i + 1): i for i, action in enumerate(CEREALS_ACTIONS)}


def setup_gpu_optimization():
    """设置GPU优化参数"""
    if torch.cuda.is_available():
        print(f"GPU available: {torch.cuda.get_device_name(0)}")
        print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        
        # 显示当前GPU使用情况
        print(f"Initial GPU memory allocated: {torch.cuda.memory_allocated(0) / 1024**3:.3f} GB")
        print(f"Initial GPU memory cached: {torch.cuda.memory_reserved(0) / 1024**3:.3f} GB")
        
        # 设置GPU优化参数
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        
        # 清理GPU缓存
        torch.cuda.empty_cache()
        print("GPU optimization configured.")
        
        # 创建一个小的tensor验证GPU可用性
        test_tensor = torch.randn(100, 100).cuda()
        _ = torch.matmul(test_tensor, test_tensor.T)
        print("✅ GPU tensor operations verified")
        
        del test_tensor
        torch.cuda.empty_cache()
    else:
        print("❌ Warning: GPU not available!")


def load_static_baseline_if_available(cfg: DictConfig):
    """如果存在静态基线模型，加载它作为初始化"""
    static_model_path = "/data2/syd_data/Breakfast_Data/Outputs/static_baseline/final_static_model.ckpt"
    
    if os.path.exists(static_model_path):
        print(f"🔄 Found static baseline model: {static_model_path}")
        print("📖 Loading static baseline weights for initialization...")
        return static_model_path
    else:
        print("ℹ️ No static baseline found, training from scratch")
        return None


def create_callbacks(cfg: DictConfig):
    """创建训练回调函数"""
    callbacks = []
    
    # 模型检查点
    checkpoint_callback = ModelCheckpoint(
        dirpath=os.path.join(cfg.output_dir, "checkpoints"),
        filename="dynamic-{epoch:02d}-{val_segment_iou:.3f}",
        monitor="val_segment_iou",
        mode="max",
        save_top_k=3,
        save_last=True,
        verbose=True
    )
    callbacks.append(checkpoint_callback)
    
    # 早停
    early_stop_callback = EarlyStopping(
        monitor="val_segment_iou",
        min_delta=0.001,
        patience=15,  # 动态方法可能需要更多耐心
        verbose=True,
        mode="max"
    )
    callbacks.append(early_stop_callback)
    
    return callbacks


def create_logger(cfg: DictConfig):
    """创建日志记录器"""
    logger = TensorBoardLogger(
        save_dir=cfg.output_dir,
        name="dynamic_method_logs",
        version=None
    )
    return logger


@hydra.main(config_path="configs", config_name="config_dynamic", version_base=None)
def main(cfg: DictConfig):
    """主训练函数"""
    print("=" * 80)
    print("🔄 动态差分更新方法训练 - A6000 GPU优化版本")
    print("=" * 80)
    
    # 设置GPU优化
    setup_gpu_optimization()
    
    # 设置随机种子
    pl.seed_everything(cfg.seed, workers=True)
    
    # 检查输出目录
    os.makedirs(cfg.output_dir, exist_ok=True)
    print(f"📁 Output directory: {cfg.output_dir}")
    
    # 检查是否有静态基线可以加载
    static_checkpoint = load_static_baseline_if_available(cfg)
    
    # 1. 数据模块设置
    print("\n📊 Setting up data module...")
    dm = BreakfastDataModule(
        data_root=cfg.data_root,
        batch_size=cfg.trainer.batch_size,
        action_map=ACTION_MAP,
        num_workers=cfg.trainer.num_workers
    )
    
    # 2. 计算或加载统计数据
    print("\n📈 Processing statistics...")
    stats_proto_path = os.path.join(cfg.stats_dir, "prototypes.pt")
    stats_w0_path = os.path.join(cfg.stats_dir, "W0.pt")
    
    if not (os.path.exists(stats_proto_path) and os.path.exists(stats_w0_path)):
        print("Computing statistics from dataset...")
        dm.setup('fit')

        # 检查数据集是否为空
        if len(dm.train_dataset) == 0:
            raise ValueError("训练数据集为空，请检查数据路径配置。数据集应位于服务器上的指定路径。")

        prototypes, W0 = compute_and_save_stats(dm.train_dataset, cfg.model.M, cfg.stats_dir)
    else:
        print("Loading existing statistics...")
        prototypes, W0 = load_stats(cfg.stats_dir)
    
    verify_stats(prototypes, W0, cfg.model.D, cfg.model.M)
    print(f"✅ Statistics verified: prototypes {prototypes.shape}, W0 {W0.shape}")
    
    # 3. 模型设置
    print("\n🧠 Setting up model...")
    task_graph = TaskGraph(W0, prototypes)
    diff_mlp = DiffMLP(cfg.model.D, cfg.model.M, cfg.model.H, cfg.model.alpha)
    
    model = TrainerModule(
        diff_mlp=diff_mlp,
        task_graph=task_graph,
        learning_rate=cfg.model.lr,
        diff_enabled=True  # 启用动态差分更新
    )
    
    # 如果有静态基线，尝试加载部分权重
    if static_checkpoint:
        try:
            # 只加载兼容的权重，忽略差分更新相关的参数
            static_state = torch.load(static_checkpoint, map_location='cpu')
            model_state = model.state_dict()
            
            loaded_keys = []
            for key in static_state['state_dict']:
                if key in model_state and model_state[key].shape == static_state['state_dict'][key].shape:
                    model_state[key] = static_state['state_dict'][key]
                    loaded_keys.append(key)
            
            model.load_state_dict(model_state)
            print(f"🔄 Loaded {len(loaded_keys)} compatible weights from static baseline")
        except Exception as e:
            print(f"⚠️ Failed to load static baseline weights: {e}")
            print("🔄 Training from scratch...")
    
    # 计算模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"📊 Model parameters: {total_params:,} total, {trainable_params:,} trainable")
    
    # 4. 设置回调和日志
    print("\n⚙️ Setting up callbacks and logger...")
    callbacks = create_callbacks(cfg)
    logger = create_logger(cfg)
    
    # 5. 创建训练器
    print("\n🏋️ Setting up trainer...")
    trainer = pl.Trainer(
        max_epochs=cfg.trainer.max_epochs,
        accelerator=cfg.trainer.accelerator,
        devices=cfg.trainer.devices,
        precision=cfg.trainer.precision,
        callbacks=callbacks,
        logger=logger,
        gradient_clip_val=cfg.trainer.gradient_clip_val,
        val_check_interval=cfg.trainer.val_check_interval,
        check_val_every_n_epoch=cfg.trainer.check_val_every_n_epoch,
        log_every_n_steps=cfg.logging.log_every_n_steps,  # 使用配置文件中的日志间隔
        default_root_dir=cfg.output_dir,
        enable_checkpointing=True,
        enable_progress_bar=True,
        enable_model_summary=True
    )
    
    # 6. 开始训练
    print("\n🚀 Starting training...")
    print(f"📊 Training configuration:")
    print(f"   - Epochs: {cfg.trainer.max_epochs}")
    print(f"   - Batch size: {cfg.trainer.batch_size}")
    print(f"   - Learning rate: {cfg.model.lr}")
    print(f"   - GPU: {cfg.trainer.devices}")
    print(f"   - Precision: {cfg.trainer.precision}")
    print(f"   - Model: Dynamic method (diff_enabled=True)")
    print(f"   - Alpha (diff factor): {cfg.model.alpha}")
    
    try:
        # 显示训练开始时的GPU使用情况
        if torch.cuda.is_available():
            print(f"🔍 Training start GPU memory: {torch.cuda.memory_allocated(0) / 1024**3:.3f} GB allocated")
        
        trainer.fit(model, datamodule=dm)
        
        # 显示训练结束时的GPU使用情况
        if torch.cuda.is_available():
            print(f"🔍 Training end GPU memory: {torch.cuda.memory_allocated(0) / 1024**3:.3f} GB allocated")
            print(f"🔍 Max GPU memory used: {torch.cuda.max_memory_allocated(0) / 1024**3:.3f} GB")
        
        # 训练完成后的总结
        print("\n" + "=" * 80)
        print("✅ 动态差分更新训练完成!")
        print(f"📁 Results saved to: {cfg.output_dir}")
        print(f"🏆 Best validation IoU: {trainer.callback_metrics.get('val_segment_iou', 'N/A')}")
        print("=" * 80)
        
        # 保存最终模型
        final_model_path = os.path.join(cfg.output_dir, "final_dynamic_model.ckpt")
        trainer.save_checkpoint(final_model_path)
        print(f"💾 Final model saved to: {final_model_path}")
        
    except KeyboardInterrupt:
        print("\n⚠️ Training interrupted by user")
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        raise


if __name__ == "__main__":
    main() 